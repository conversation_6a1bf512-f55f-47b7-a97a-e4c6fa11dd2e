'use client';

import { useState, useEffect } from 'react';
import { apiClient } from '@/lib/api';

interface CapexOptimizationProps {
  onOptimizationUpdate?: (data: any) => void;
}

export function CapexOptimization({ onOptimizationUpdate }: CapexOptimizationProps) {
  const [currentStep, setCurrentStep] = useState<'upload' | 'scoring' | 'weights' | 'optimize' | 'whatif'>('upload');
  const [capexData, setCapexData] = useState<any[]>([]);
  const [scoredProjects, setScoredProjects] = useState<any[]>([]);
  const [weights, setWeights] = useState({
    strategic_alignment: 0.3,
    roi_potential: 0.4,
    risk_level: 0.2,
    implementation_ease: 0.1,
  });
  const [optimizationGoal, setOptimizationGoal] = useState<'maximize_roi' | 'minimize_risk' | 'balanced'>('balanced');
  const [optimizedAllocation, setOptimizedAllocation] = useState<any>(null);
  const [whatIfAllocation, setWhatIfAllocation] = useState<any>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [weightPreview, setWeightPreview] = useState<any[]>([]);

  // Initialize weight preview when scored projects are available
  useEffect(() => {
    if (scoredProjects.length > 0) {
      updateWeightPreview(weights);
    }
  }, [scoredProjects]);

  // Initialize what-if allocation when optimized allocation is available
  useEffect(() => {
    if (optimizedAllocation && !whatIfAllocation) {
      const whatIfCopy = JSON.parse(JSON.stringify(optimizedAllocation));
      // Ensure all budget values are clean numbers
      whatIfCopy.projects = whatIfCopy.projects.map((p: any) => ({
        ...p,
        budget: typeof p.budget === 'number' && p.budget > 0 ? p.budget : 1000000 // Default $1M if invalid
      }));
      setWhatIfAllocation(whatIfCopy);
    }
  }, [optimizedAllocation, whatIfAllocation]);

  // Calculate total weights for validation
  const totalWeights = Object.values(weights).reduce((sum, weight) => sum + weight, 0);
  const weightsValid = Math.abs(totalWeights - 1.0) < 0.01;

  const steps = [
    { id: 'upload', label: 'Upload CAPEX Data', icon: '📁', completed: capexData.length > 0 },
    { id: 'scoring', label: 'AI Scoring', icon: '🤖', completed: scoredProjects.length > 0 },
    { id: 'weights', label: 'Set Weights', icon: '⚖️', completed: weightsValid },
    { id: 'optimize', label: 'Optimize Allocation', icon: '🎯', completed: optimizedAllocation !== null },
    { id: 'whatif', label: 'What-If Analysis', icon: '🔍', completed: whatIfAllocation !== null },
  ];

  const runAIScoring = async () => {
    try {
      setIsProcessing(true);

      // Try to use backend AI scoring service
      try {
        const response = await apiClient.post('/agents/capex/score', {
          projects: capexData,
          scoring_criteria: {
            strategic_alignment: 'Alignment with digital transformation and business strategy',
            roi_potential: 'Expected return on investment based on market analysis',
            risk_level: 'Implementation and business risks assessment',
            implementation_ease: 'Complexity and resource requirements'
          }
        });

        if (response.data.success) {
          setScoredProjects(response.data.data.scored_projects);
          setCurrentStep('weights');
          return;
        }
      } catch (backendError) {
        console.warn('Backend AI scoring failed, using enhanced simulation:', backendError);
      }

      // Enhanced simulation with more realistic scoring logic
      const scored = capexData.map((project, index) => {
        // Base scores influenced by project characteristics
        let strategicScore = 70;
        let roiScore = 65;
        let riskScore = 45; // Lower is better
        let easeScore = 75;

        // Adjust based on project category and priority
        if (project.category === 'Infrastructure') {
          strategicScore += 15;
          riskScore += 10;
          easeScore -= 10;
        } else if (project.category === 'Technology') {
          roiScore += 20;
          strategicScore += 10;
          riskScore += 5;
        } else if (project.category === 'Analytics') {
          roiScore += 25;
          strategicScore += 20;
          easeScore += 10;
        }

        if (project.priority === 'High') {
          strategicScore += 10;
          roiScore += 10;
        } else if (project.priority === 'Low') {
          strategicScore -= 5;
          roiScore -= 5;
          riskScore -= 10; // Lower risk for low priority
        }

        // Add some randomness but keep it realistic
        strategicScore = Math.min(95, Math.max(50, strategicScore + (Math.random() - 0.5) * 20));
        roiScore = Math.min(95, Math.max(45, roiScore + (Math.random() - 0.5) * 25));
        riskScore = Math.min(80, Math.max(20, riskScore + (Math.random() - 0.5) * 30));
        easeScore = Math.min(95, Math.max(40, easeScore + (Math.random() - 0.5) * 20));

        const aiScore = (strategicScore + roiScore + (100 - riskScore) + easeScore) / 4;

        return {
          ...project,
          ai_score: Math.round(aiScore),
          strategic_alignment: Math.round(strategicScore),
          roi_potential: Math.round(roiScore),
          risk_level: Math.round(riskScore),
          implementation_ease: Math.round(easeScore),
          justification: [
            `Strategic alignment: ${strategicScore > 80 ? 'Excellent' : strategicScore > 65 ? 'Good' : 'Moderate'} fit with business objectives`,
            `ROI potential: ${roiScore > 80 ? 'High' : roiScore > 60 ? 'Medium' : 'Conservative'} expected returns based on ${project.category.toLowerCase()} investments`,
            `Implementation risk: ${riskScore < 40 ? 'Low' : riskScore < 60 ? 'Medium' : 'High'} complexity with ${project.priority.toLowerCase()} priority status`
          ]
        };
      });

      setScoredProjects(scored);
      setCurrentStep('weights');

    } catch (error) {
      console.error('AI scoring error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const runOptimization = async () => {
    try {
      setIsProcessing(true);
      
      // Calculate weighted scores
      const weightedProjects = scoredProjects.map(project => ({
        ...project,
        weighted_score: (
          project.strategic_alignment * weights.strategic_alignment +
          project.roi_potential * weights.roi_potential +
          (100 - project.risk_level) * weights.risk_level + // Invert risk
          project.implementation_ease * weights.implementation_ease
        )
      }));

      // Sort by weighted score
      const optimized = weightedProjects.sort((a, b) => b.weighted_score - a.weighted_score);
      
      const allocation = {
        projects: optimized,
        total_budget: optimized.reduce((sum, p) => sum + (p.budget || 0), 0),
        expected_roi: optimized.reduce((sum, p) => sum + (p.roi_potential * (p.budget || 0) / 100), 0),
        risk_score: optimized.reduce((sum, p) => sum + p.risk_level, 0) / optimized.length,
      };

      setOptimizedAllocation(allocation);
      // Create a clean deep copy for what-if analysis with proper budget values
      const whatIfCopy = JSON.parse(JSON.stringify(allocation));
      // Ensure all budget values are numbers and reasonable
      whatIfCopy.projects = whatIfCopy.projects.map((p: any) => ({
        ...p,
        budget: typeof p.budget === 'number' ? p.budget : 0
      }));
      setWhatIfAllocation(whatIfCopy);
      setCurrentStep('whatif');
      onOptimizationUpdate?.(optimized);
      
    } catch (error) {
      console.error('Optimization error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  // Helper function to update weights and maintain total = 1.0
  const updateWeight = (key: string, value: number) => {
    const newWeights = { ...weights };
    const oldValue = newWeights[key as keyof typeof weights];
    const difference = value - oldValue;

    // Update the target weight
    newWeights[key as keyof typeof weights] = value;

    // Distribute the difference across other weights proportionally
    const otherKeys = Object.keys(newWeights).filter(k => k !== key);
    const otherTotal = otherKeys.reduce((sum, k) => sum + newWeights[k as keyof typeof weights], 0);

    if (otherTotal > 0) {
      otherKeys.forEach(k => {
        const proportion = newWeights[k as keyof typeof weights] / otherTotal;
        newWeights[k as keyof typeof weights] = Math.max(0, newWeights[k as keyof typeof weights] - (difference * proportion));
      });
    }

    setWeights(newWeights);
    updateWeightPreview(newWeights);
  };

  // Update weight preview to show how rankings would change
  const updateWeightPreview = (newWeights: typeof weights) => {
    if (scoredProjects.length === 0) return;

    const previewProjects = scoredProjects.map(project => ({
      ...project,
      weighted_score: (
        project.strategic_alignment * newWeights.strategic_alignment +
        project.roi_potential * newWeights.roi_potential +
        (100 - project.risk_level) * newWeights.risk_level +
        project.implementation_ease * newWeights.implementation_ease
      )
    })).sort((a, b) => b.weighted_score - a.weighted_score);

    setWeightPreview(previewProjects);
  };

  // What-if analysis: update project budget and recalculate metrics
  const updateWhatIfBudget = (projectId: number, newBudget: number) => {
    if (!whatIfAllocation) return;

    // Ensure the new budget is a reasonable number
    const sanitizedBudget = Math.max(0, Math.min(newBudget, 100000000)); // Cap at $100M

    const updatedProjects = whatIfAllocation.projects.map((p: any) =>
      p.id === projectId ? { ...p, budget: sanitizedBudget } : p
    );

    const newAllocation = {
      ...whatIfAllocation,
      projects: updatedProjects,
      total_budget: updatedProjects.reduce((sum: number, p: any) => sum + (p.budget || 0), 0),
      expected_roi: updatedProjects.reduce((sum: number, p: any) => sum + (p.roi_potential * (p.budget || 0) / 100), 0),
      risk_score: updatedProjects.reduce((sum: number, p: any) => sum + p.risk_level, 0) / updatedProjects.length,
    };

    setWhatIfAllocation(newAllocation);
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'upload':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Upload CAPEX Data</h3>
            
            {capexData.length === 0 ? (
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                <div className="text-4xl mb-4">📁</div>
                <h4 className="text-lg font-medium text-gray-900 mb-2">Upload CAPEX Planning Data</h4>
                <p className="text-gray-600 mb-4">
                  Upload your CAPEX planning spreadsheet with project details, budgets, and timelines.
                </p>
                <button
                  onClick={() => {
                    // Simulate data upload for demo
                    const sampleData = [
                      { id: 1, name: '5G Network Expansion', budget: 15000000, category: 'Infrastructure', priority: 'High' },
                      { id: 2, name: 'Digital Platform Upgrade', budget: 8000000, category: 'Technology', priority: 'Medium' },
                      { id: 3, name: 'Customer Experience Center', budget: 3000000, category: 'Customer', priority: 'Low' },
                      { id: 4, name: 'Data Analytics Platform', budget: 5000000, category: 'Analytics', priority: 'High' },
                      { id: 5, name: 'Security Infrastructure', budget: 4000000, category: 'Security', priority: 'Medium' },
                    ];
                    setCapexData(sampleData);
                  }}
                  className="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
                >
                  📁 Upload Sample CAPEX Data
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h4 className="font-medium text-green-800">✅ CAPEX Data Loaded</h4>
                  <p className="text-green-700 text-sm">
                    {capexData.length} projects loaded with total budget of ${capexData.reduce((sum, p) => sum + p.budget, 0).toLocaleString()}
                  </p>
                </div>
                
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Project</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Budget</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Category</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Priority</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {capexData.map((project) => (
                        <tr key={project.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {project.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${project.budget.toLocaleString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {project.category}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              project.priority === 'High' ? 'bg-red-100 text-red-800' :
                              project.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-green-100 text-green-800'
                            }`}>
                              {project.priority}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                
                <button
                  onClick={() => setCurrentStep('scoring')}
                  className="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
                >
                  Next: AI Scoring →
                </button>
              </div>
            )}
          </div>
        );

      case 'scoring':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">AI Scoring & Analysis</h3>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-800 mb-2">🤖 AI Scoring Criteria</h4>
              <ul className="text-blue-700 text-sm space-y-1">
                <li>• <strong>Strategic Alignment:</strong> How well the project aligns with business strategy</li>
                <li>• <strong>ROI Potential:</strong> Expected return on investment based on historical data</li>
                <li>• <strong>Risk Level:</strong> Implementation and business risks</li>
                <li>• <strong>Implementation Ease:</strong> Complexity and resource requirements</li>
              </ul>
            </div>

            {scoredProjects.length === 0 ? (
              <div className="text-center py-8">
                <button
                  onClick={runAIScoring}
                  disabled={isProcessing}
                  className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 disabled:bg-gray-400 transition-colors"
                >
                  {isProcessing ? '🤖 AI Analyzing Projects...' : '🤖 Run AI Scoring'}
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h4 className="font-medium text-green-800">✅ AI Scoring Complete</h4>
                  <p className="text-green-700 text-sm">
                    All {scoredProjects.length} projects have been scored and analyzed.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {scoredProjects.map((project) => (
                    <div key={project.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-3">
                        <h4 className="font-medium text-gray-900">{project.name}</h4>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-indigo-600">{project.ai_score}</div>
                          <div className="text-xs text-gray-500">AI Score</div>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-2 text-sm mb-3">
                        <div>Strategic: {project.strategic_alignment}/100</div>
                        <div>ROI: {project.roi_potential}/100</div>
                        <div>Risk: {project.risk_level}/100</div>
                        <div>Ease: {project.implementation_ease}/100</div>
                      </div>
                      
                      <div className="text-xs text-gray-600">
                        <strong>Justification:</strong>
                        <ul className="mt-1 space-y-1">
                          {project.justification.map((reason: string, index: number) => (
                            <li key={index}>• {reason}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>

                <button
                  onClick={() => setCurrentStep('weights')}
                  className="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
                >
                  Next: Set Weights →
                </button>
              </div>
            )}
          </div>
        );

      case 'weights':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Set Strategic Weights</h3>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-800 mb-2">⚖️ Weight Configuration</h4>
              <p className="text-yellow-700 text-sm">
                Adjust the importance of each scoring criteria based on your strategic priorities. Weights must sum to 100%.
              </p>
            </div>

            {/* Weight validation indicator */}
            <div className={`p-3 rounded-lg border ${
              weightsValid
                ? 'bg-green-50 border-green-200 text-green-800'
                : 'bg-red-50 border-red-200 text-red-800'
            }`}>
              <div className="flex items-center justify-between">
                <span className="font-medium">
                  {weightsValid ? '✅ Weights Valid' : '⚠️ Weights Invalid'}
                </span>
                <span className="text-sm">
                  Total: {(totalWeights * 100).toFixed(1)}% {weightsValid ? '' : '(must be 100%)'}
                </span>
              </div>
            </div>

            <div className="space-y-4">
              {Object.entries(weights).map(([key, value]) => (
                <div key={key} className="flex items-center space-x-4">
                  <div className="w-40">
                    <label className="text-sm font-medium text-gray-700 capitalize">
                      {key.replace('_', ' ')}
                    </label>
                  </div>
                  <div className="flex-1">
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.05"
                      value={value}
                      onChange={(e) => updateWeight(key, Number(e.target.value))}
                      className="w-full"
                    />
                  </div>
                  <div className="w-16 text-right">
                    <span className="text-sm font-medium">{(value * 100).toFixed(1)}%</span>
                  </div>
                </div>
              ))}
            </div>

            {/* Weight preview */}
            {weightPreview.length > 0 && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-3">📊 Ranking Preview</h4>
                <div className="space-y-2">
                  {weightPreview.slice(0, 5).map((project, index) => (
                    <div key={project.id} className="flex items-center justify-between text-sm">
                      <div className="flex items-center">
                        <span className="w-6 h-6 bg-indigo-100 text-indigo-800 rounded-full flex items-center justify-center text-xs font-bold mr-2">
                          {index + 1}
                        </span>
                        <span>{project.name}</span>
                      </div>
                      <span className="font-medium">{project.weighted_score.toFixed(1)}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Optimization Goal</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {[
                  { id: 'maximize_roi', label: 'Maximize ROI', icon: '💰', desc: 'Focus on highest return projects' },
                  { id: 'minimize_risk', label: 'Minimize Risk', icon: '🛡️', desc: 'Prioritize low-risk projects' },
                  { id: 'balanced', label: 'Balanced', icon: '⚖️', desc: 'Balance risk and return' },
                ].map((goal) => (
                  <div
                    key={goal.id}
                    className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                      optimizationGoal === goal.id
                        ? 'border-indigo-500 bg-indigo-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setOptimizationGoal(goal.id as any)}
                  >
                    <div className="text-center">
                      <div className="text-2xl mb-2">{goal.icon}</div>
                      <h5 className="font-medium text-gray-900">{goal.label}</h5>
                      <p className="text-sm text-gray-600 mt-1">{goal.desc}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex gap-4">
              <button
                onClick={() => setCurrentStep('scoring')}
                className="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 transition-colors"
              >
                ← Back
              </button>
              <button
                onClick={() => setCurrentStep('optimize')}
                disabled={!weightsValid}
                className={`px-6 py-2 rounded-lg transition-colors ${
                  weightsValid
                    ? 'bg-indigo-600 text-white hover:bg-indigo-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                Next: Optimize →
              </button>
            </div>
          </div>
        );

      case 'optimize':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Optimize CAPEX Allocation</h3>
            
            {!optimizedAllocation ? (
              <div className="text-center py-8">
                <button
                  onClick={runOptimization}
                  disabled={isProcessing}
                  className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 disabled:bg-gray-400 transition-colors"
                >
                  {isProcessing ? '🎯 Optimizing Allocation...' : '🎯 Run Optimization'}
                </button>
              </div>
            ) : (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-white border rounded-lg p-6">
                    <h4 className="font-medium text-gray-900 mb-2">💰 Total Budget</h4>
                    <div className="text-2xl font-bold text-green-600">
                      ${optimizedAllocation.total_budget.toLocaleString()}
                    </div>
                  </div>
                  <div className="bg-white border rounded-lg p-6">
                    <h4 className="font-medium text-gray-900 mb-2">📈 Expected ROI</h4>
                    <div className="text-2xl font-bold text-blue-600">
                      ${optimizedAllocation.expected_roi.toLocaleString()}
                    </div>
                  </div>
                  <div className="bg-white border rounded-lg p-6">
                    <h4 className="font-medium text-gray-900 mb-2">⚠️ Risk Score</h4>
                    <div className="text-2xl font-bold text-orange-600">
                      {optimizedAllocation.risk_score.toFixed(1)}/100
                    </div>
                  </div>
                </div>

                <div className="bg-white border rounded-lg p-6">
                  <h4 className="font-medium text-gray-900 mb-4">🏆 Prioritized Project List</h4>
                  <div className="space-y-3">
                    {optimizedAllocation.projects.map((project: any, index: number) => (
                      <div key={project.id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-indigo-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">
                            {index + 1}
                          </div>
                          <div>
                            <h5 className="font-medium text-gray-900">{project.name}</h5>
                            <p className="text-sm text-gray-600">
                              Score: {project.weighted_score.toFixed(1)} | Budget: ${project.budget.toLocaleString()}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium text-indigo-600">
                            Priority {index + 1}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <button
                  onClick={() => setCurrentStep('whatif')}
                  className="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
                >
                  Next: What-If Analysis →
                </button>
              </div>
            )}
          </div>
        );

      case 'whatif':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">What-If Analysis</h3>

            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <h4 className="font-medium text-purple-800 mb-2">🔍 Interactive Analysis</h4>
              <p className="text-purple-700 text-sm">
                Adjust project allocations and see real-time impact on ROI and risk. Changes are calculated instantly.
              </p>
            </div>

            {/* Comparison Summary */}
            {whatIfAllocation && optimizedAllocation && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-800 mb-3">📊 Scenario Comparison</h4>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <div className="text-blue-700 font-medium">Budget Change</div>
                    <div className={`text-lg font-bold ${
                      whatIfAllocation.total_budget > optimizedAllocation.total_budget ? 'text-red-600' :
                      whatIfAllocation.total_budget < optimizedAllocation.total_budget ? 'text-green-600' : 'text-gray-600'
                    }`}>
                      {whatIfAllocation.total_budget > optimizedAllocation.total_budget ? '+' : ''}
                      ${(whatIfAllocation.total_budget - optimizedAllocation.total_budget).toLocaleString()}
                    </div>
                  </div>
                  <div>
                    <div className="text-blue-700 font-medium">ROI Change</div>
                    <div className={`text-lg font-bold ${
                      whatIfAllocation.expected_roi > optimizedAllocation.expected_roi ? 'text-green-600' :
                      whatIfAllocation.expected_roi < optimizedAllocation.expected_roi ? 'text-red-600' : 'text-gray-600'
                    }`}>
                      {whatIfAllocation.expected_roi > optimizedAllocation.expected_roi ? '+' : ''}
                      ${(whatIfAllocation.expected_roi - optimizedAllocation.expected_roi).toLocaleString()}
                    </div>
                  </div>
                  <div>
                    <div className="text-blue-700 font-medium">Risk Change</div>
                    <div className={`text-lg font-bold ${
                      whatIfAllocation.risk_score > optimizedAllocation.risk_score ? 'text-red-600' :
                      whatIfAllocation.risk_score < optimizedAllocation.risk_score ? 'text-green-600' : 'text-gray-600'
                    }`}>
                      {whatIfAllocation.risk_score > optimizedAllocation.risk_score ? '+' : ''}
                      {(whatIfAllocation.risk_score - optimizedAllocation.risk_score).toFixed(1)}
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Adjust Project Budgets</h4>
                {whatIfAllocation?.projects.slice(0, 5).map((project: any) => {
                  // Calculate reasonable min/max values for the slider
                  const originalBudget = optimizedAllocation?.projects.find((p: any) => p.id === project.id)?.budget || project.budget;
                  const minBudget = Math.max(0, originalBudget * 0.1); // 10% of original
                  const maxBudget = originalBudget * 2; // 200% of original
                  const currentBudget = project.budget || originalBudget;

                  return (
                    <div key={project.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-center mb-2">
                        <h5 className="font-medium text-gray-900">{project.name}</h5>
                        <span className="text-sm text-gray-600">${currentBudget.toLocaleString()}</span>
                      </div>
                      <input
                        type="range"
                        min={minBudget}
                        max={maxBudget}
                        value={currentBudget}
                        step={Math.max(1000, originalBudget * 0.01)} // 1% steps or $1K minimum
                        className="w-full"
                        onChange={(e) => updateWhatIfBudget(project.id, Number(e.target.value))}
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>${minBudget.toLocaleString()}</span>
                        <span>${maxBudget.toLocaleString()}</span>
                      </div>
                      <div className="mt-2 text-xs text-gray-600">
                        <div>Score: {project.weighted_score?.toFixed(1) || 'N/A'} |
                        ROI: {project.roi_potential}/100 |
                        Risk: {project.risk_level}/100</div>
                        <div className="text-blue-600 mt-1">
                          Original: ${originalBudget.toLocaleString()} |
                          Change: {currentBudget > originalBudget ? '+' : ''}${(currentBudget - originalBudget).toLocaleString()}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Real-time Impact</h4>
                <div className="border rounded-lg p-4">
                  <h5 className="font-medium text-gray-900 mb-3">Current Metrics</h5>
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between items-center">
                      <span>Total Budget:</span>
                      <span className="font-medium">${whatIfAllocation?.total_budget.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Expected ROI:</span>
                      <span className="font-medium text-green-600">
                        ${whatIfAllocation?.expected_roi.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Risk Score:</span>
                      <span className={`font-medium ${
                        (whatIfAllocation?.risk_score || 0) > 60 ? 'text-red-600' :
                        (whatIfAllocation?.risk_score || 0) > 40 ? 'text-orange-600' : 'text-green-600'
                      }`}>
                        {whatIfAllocation?.risk_score.toFixed(1)}/100
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>ROI Ratio:</span>
                      <span className="font-medium text-blue-600">
                        {((whatIfAllocation?.expected_roi || 0) / (whatIfAllocation?.total_budget || 1) * 100).toFixed(2)}%
                      </span>
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h5 className="font-medium text-gray-900 mb-3">Portfolio Health</h5>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Budget Efficiency:</span>
                      <span className={`font-medium ${
                        ((whatIfAllocation?.expected_roi || 0) / (whatIfAllocation?.total_budget || 1)) > 0.15 ? 'text-green-600' : 'text-orange-600'
                      }`}>
                        {((whatIfAllocation?.expected_roi || 0) / (whatIfAllocation?.total_budget || 1)) > 0.15 ? 'High' : 'Medium'}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Risk Level:</span>
                      <span className={`font-medium ${
                        (whatIfAllocation?.risk_score || 0) < 40 ? 'text-green-600' :
                        (whatIfAllocation?.risk_score || 0) < 60 ? 'text-orange-600' : 'text-red-600'
                      }`}>
                        {(whatIfAllocation?.risk_score || 0) < 40 ? 'Low' :
                         (whatIfAllocation?.risk_score || 0) < 60 ? 'Medium' : 'High'}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Diversification:</span>
                      <span className="font-medium text-blue-600">
                        {whatIfAllocation?.projects.length || 0} projects
                      </span>
                    </div>
                  </div>
                </div>

                {/* Reset button */}
                <button
                  onClick={() => {
                    if (optimizedAllocation) {
                      const resetCopy = JSON.parse(JSON.stringify(optimizedAllocation));
                      // Ensure all budget values are clean numbers
                      resetCopy.projects = resetCopy.projects.map((p: any) => ({
                        ...p,
                        budget: typeof p.budget === 'number' ? p.budget : 0
                      }));
                      setWhatIfAllocation(resetCopy);
                    }
                  }}
                  className="w-full bg-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors"
                >
                  🔄 Reset to Optimized
                </button>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">🏗️ CAPEX Optimization</h2>
        <p className="text-gray-600">
          AI-powered CAPEX prioritization with scoring, optimization, and what-if analysis.
        </p>
      </div>

      {/* Progress Steps */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-center justify-between mb-8">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div
                className={`
                  flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors cursor-pointer
                  ${step.completed || currentStep === step.id
                    ? 'bg-indigo-600 border-indigo-600 text-white'
                    : 'bg-white border-gray-300 text-gray-400'
                  }
                `}
                onClick={() => setCurrentStep(step.id as any)}
              >
                {step.completed ? '✓' : step.icon}
              </div>
              <div className="ml-3 hidden md:block">
                <div className={`text-sm font-medium ${
                  step.completed || currentStep === step.id ? 'text-indigo-600' : 'text-gray-400'
                }`}>
                  {step.label}
                </div>
              </div>
              {index < steps.length - 1 && (
                <div className={`w-12 h-0.5 mx-4 ${
                  step.completed ? 'bg-indigo-600' : 'bg-gray-300'
                }`} />
              )}
            </div>
          ))}
        </div>

        {/* Step Content */}
        {renderStepContent()}
      </div>
    </div>
  );
}
